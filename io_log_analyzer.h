#ifndef IO_LOG_ANALYZER_H
#define IO_LOG_ANALYZER_H

#include "io_custom_component_global.h"
#include <QWidget>
#include <QFileDialog>
#include <QTextStream>
#include <QMessageBox>
#include <QDebug>

#include "../io_base_controls/io_base_controls.h"
#include "../io_dialog/io_dialog.h"
#include "../io_custom_widgets/io_table_view/my_table_model_big.h"


namespace Ui {
class IoLogAnalyzer;
}

class IO_CUSTOM_COMPONENTSHARED_EXPORT IoLogAnalyzer : public IoBaseControls
{
    Q_OBJECT

public:
    explicit IoLogAnalyzer(QWidget* parent = 0);
    ~IoLogAnalyzer();

private:
    Ui::IoLogAnalyzer* ui;

public:
    void init(QString project);

private:
    void init_connect();
    void load_csv_file(const QString& path);   // 新增优化加载方法

private:
    QString project_;
};

#endif   // KTX_PARSING_LOG_H
