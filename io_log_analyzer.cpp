#include "io_log_analyzer.h"
#include "ui_io_log_analyzer.h"


IoLogAnalyzer::IoLogAnalyzer(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::IoLogAnalyzer)
{
    ui->setupUi(this);
    init_connect();
}

IoLogAnalyzer::~IoLogAnalyzer() { delete ui; }

void IoLogAnalyzer::init(QString project) { project_ = project; }

void IoLogAnalyzer::init_connect()
{
    // 选择日志文件
    connect(ui->PB_Select_File, &QPushButton::clicked, this, [=]() {
        QString path = IoDialog::get_select_file("(*.csv);;(*.*)");
        if (path.isEmpty()) {
            return;
        }
        ui->LE_Log_Path->setText(path);

        // 使用优化的加载方法处理大文件
        load_csv_file(path);
    });
}

void IoLogAnalyzer::load_csv_file(const QString& path)
{
    // 首先计算总行数用于进度显示
    qDebug() << "正在计算文件行数...";
    int total_lines = count_csv_lines(path);
    qDebug() << "文件总行数:" << total_lines;

    if (total_lines == 0) {
        QMessageBox::warning(this, tr("错误"), tr("文件为空或无法读取: %1").arg(path));
        return;
    }

    QFile file(path);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件: %1").arg(path));
        return;
    }

    // 清空现有数据
    ui->ITV_csv->delete_all_row();

    // 启用超级内存模式
    ui->ITV_csv->get_table_model()->enableUltraMemoryMode(true);

    QTextStream in(&file);
    // 尝试自动检测UTF-16/UTF-8 BOM
    in.setAutoDetectUnicode(true);
    //
    QString line;
    bool    first_line = true;
    int     row_count  = 0;
    int     processed_lines = 0;  // 已处理的行数（包括表头）

    while (!in.atEnd()) {
        line = in.readLine();
        if (line.trimmed().isEmpty()) {
            continue;
        }

        processed_lines++;  // 增加已处理行数

        QStringList fields = line.split(',');

        if (first_line) {
            // 处理表头
            QVector<QString> headers;
            for (const QString& field : fields) {
                QString cleaned_field = field.trimmed();
                if (cleaned_field.startsWith('"') && cleaned_field.endsWith('"')) {
                    cleaned_field = cleaned_field.mid(1, cleaned_field.length() - 2);
                }
                headers.append(cleaned_field);
            }
            ui->ITV_csv->set_title_list(headers);
            first_line = false;
        }
        else {
            // 处理数据行 - 使用优化存储
            QVector<CellData> row_data;
            for (const QString& field : fields) {
                QString cleaned_field = field.trimmed();
                if (cleaned_field.startsWith('"') && cleaned_field.endsWith('"')) {
                    cleaned_field = cleaned_field.mid(1, cleaned_field.length() - 2);
                }
                // 自动检测数据类型并创建优化的CellData
                row_data.append(MyTableModelBig::parse_cell_data(cleaned_field));
            }

            // 使用优化的添加方法
            ui->ITV_csv->get_table_model()->append_row(row_data);
            row_count++;

            // 每1000行输出一次进度信息，包括百分比
            if (row_count % 1000 == 0) {
                double progress = (double)processed_lines / total_lines * 100.0;
                qDebug() << QString("已加载 %1 行数据，进度: %2% (%3/%4)")
                               .arg(row_count)
                               .arg(progress, 0, 'f', 1)
                               .arg(processed_lines)
                               .arg(total_lines);
            }
        }
    }

    file.close();
    qDebug() << QString("优化CSV文件加载完成！数据行: %1，总行数: %2 (包括表头)")
                   .arg(row_count)
                   .arg(total_lines);

    // 输出内存使用统计
    ui->ITV_csv->get_table_model()->printMemoryStats();
}

int IoLogAnalyzer::count_csv_lines(const QString& path)
{
    QFile file(path);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return 0;
    }

    QTextStream in(&file);
    in.setAutoDetectUnicode(true);

    int line_count = 0;
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (!line.trimmed().isEmpty()) {
            line_count++;
        }
    }

    file.close();
    return line_count;
}
